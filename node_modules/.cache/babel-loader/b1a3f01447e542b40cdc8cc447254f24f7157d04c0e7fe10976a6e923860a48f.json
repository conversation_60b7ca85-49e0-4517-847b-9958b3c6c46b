{"ast": null, "code": "import React,{useState}from'react';import'./Profile.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Profile=()=>{const[user]=useState({name:'张三',phone:'138****8888',avatar:'👤',level:'黄金会员',points:1250});const[orders]=useState([{id:'1',date:'2024-01-15',items:['阿萨姆奶茶','波霸奶茶'],total:38,status:'已完成'},{id:'2',date:'2024-01-10',items:['蜂蜜柠檬茶','芒果绿茶'],total:38,status:'已完成'},{id:'3',date:'2024-01-05',items:['黑糖珍珠撞奶'],total:25,status:'已完成'}]);const menuItems=[{id:'favorites',name:'我的收藏',icon:'❤️'},{id:'address',name:'收货地址',icon:'📍'},{id:'coupons',name:'优惠券',icon:'🎫'},{id:'help',name:'帮助中心',icon:'❓'},{id:'feedback',name:'意见反馈',icon:'💬'},{id:'settings',name:'设置',icon:'⚙️'}];return/*#__PURE__*/_jsx(\"div\",{className:\"profile\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"profile-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"user-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"avatar\",children:user.avatar}),/*#__PURE__*/_jsxs(\"div\",{className:\"user-details\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"user-name\",children:user.name}),/*#__PURE__*/_jsx(\"p\",{className:\"user-phone\",children:user.phone}),/*#__PURE__*/_jsxs(\"div\",{className:\"user-level\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"level-badge\",children:user.level}),/*#__PURE__*/_jsxs(\"span\",{className:\"points\",children:[\"\\u79EF\\u5206: \",user.points]})]})]})]}),/*#__PURE__*/_jsx(\"button\",{className:\"edit-btn\",children:\"\\u7F16\\u8F91\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stats-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-number\",children:orders.length}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u603B\\u8BA2\\u5355\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-number\",children:orders.filter(o=>o.status==='已完成').length}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u5DF2\\u5B8C\\u6210\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-number\",children:user.points}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u79EF\\u5206\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"menu-section\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"section-title\",children:\"\\u529F\\u80FD\\u83DC\\u5355\"}),/*#__PURE__*/_jsx(\"div\",{className:\"menu-grid\",children:menuItems.map(item=>/*#__PURE__*/_jsxs(\"button\",{className:\"menu-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"menu-icon\",children:item.icon}),/*#__PURE__*/_jsx(\"span\",{className:\"menu-name\",children:item.name}),/*#__PURE__*/_jsx(\"span\",{className:\"menu-arrow\",children:\"\\u203A\"})]},item.id))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"orders-section\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"section-title\",children:\"\\u6700\\u8FD1\\u8BA2\\u5355\"}),/*#__PURE__*/_jsx(\"div\",{className:\"orders-list\",children:orders.map(order=>/*#__PURE__*/_jsxs(\"div\",{className:\"order-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"order-header\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"order-date\",children:order.date}),/*#__PURE__*/_jsx(\"span\",{className:`order-status ${order.status==='已完成'?'completed':''}`,children:order.status})]}),/*#__PURE__*/_jsx(\"div\",{className:\"order-items\",children:order.items.join(', ')}),/*#__PURE__*/_jsxs(\"div\",{className:\"order-total\",children:[\"\\u603B\\u8BA1: \\xA5\",order.total]})]},order.id))}),/*#__PURE__*/_jsx(\"button\",{className:\"view-all-btn\",children:\"\\u67E5\\u770B\\u5168\\u90E8\\u8BA2\\u5355\"})]})]})});};export default Profile;", "map": {"version": 3, "names": ["React", "useState", "jsx", "_jsx", "jsxs", "_jsxs", "Profile", "user", "name", "phone", "avatar", "level", "points", "orders", "id", "date", "items", "total", "status", "menuItems", "icon", "className", "children", "length", "filter", "o", "map", "item", "order", "join"], "sources": ["/Users/<USER>/Desktop/code/vibe/yidiandian/src/pages/Profile.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport './Profile.css';\n\nconst Profile: React.FC = () => {\n  const [user] = useState({\n    name: '张三',\n    phone: '138****8888',\n    avatar: '👤',\n    level: '黄金会员',\n    points: 1250,\n  });\n\n  const [orders] = useState([\n    {\n      id: '1',\n      date: '2024-01-15',\n      items: ['阿萨姆奶茶', '波霸奶茶'],\n      total: 38,\n      status: '已完成',\n    },\n    {\n      id: '2',\n      date: '2024-01-10',\n      items: ['蜂蜜柠檬茶', '芒果绿茶'],\n      total: 38,\n      status: '已完成',\n    },\n    {\n      id: '3',\n      date: '2024-01-05',\n      items: ['黑糖珍珠撞奶'],\n      total: 25,\n      status: '已完成',\n    },\n  ]);\n\n  const menuItems = [\n    { id: 'favorites', name: '我的收藏', icon: '❤️' },\n    { id: 'address', name: '收货地址', icon: '📍' },\n    { id: 'coupons', name: '优惠券', icon: '🎫' },\n    { id: 'help', name: '帮助中心', icon: '❓' },\n    { id: 'feedback', name: '意见反馈', icon: '💬' },\n    { id: 'settings', name: '设置', icon: '⚙️' },\n  ];\n\n  return (\n    <div className=\"profile\">\n      <div className=\"container\">\n        <div className=\"profile-header\">\n          <div className=\"user-info\">\n            <div className=\"avatar\">{user.avatar}</div>\n            <div className=\"user-details\">\n              <h2 className=\"user-name\">{user.name}</h2>\n              <p className=\"user-phone\">{user.phone}</p>\n              <div className=\"user-level\">\n                <span className=\"level-badge\">{user.level}</span>\n                <span className=\"points\">积分: {user.points}</span>\n              </div>\n            </div>\n          </div>\n          <button className=\"edit-btn\">编辑</button>\n        </div>\n\n        <div className=\"stats-section\">\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">{orders.length}</div>\n            <div className=\"stat-label\">总订单</div>\n          </div>\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">{orders.filter(o => o.status === '已完成').length}</div>\n            <div className=\"stat-label\">已完成</div>\n          </div>\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">{user.points}</div>\n            <div className=\"stat-label\">积分</div>\n          </div>\n        </div>\n\n        <div className=\"menu-section\">\n          <h3 className=\"section-title\">功能菜单</h3>\n          <div className=\"menu-grid\">\n            {menuItems.map((item) => (\n              <button key={item.id} className=\"menu-item\">\n                <span className=\"menu-icon\">{item.icon}</span>\n                <span className=\"menu-name\">{item.name}</span>\n                <span className=\"menu-arrow\">›</span>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"orders-section\">\n          <h3 className=\"section-title\">最近订单</h3>\n          <div className=\"orders-list\">\n            {orders.map((order) => (\n              <div key={order.id} className=\"order-item\">\n                <div className=\"order-header\">\n                  <span className=\"order-date\">{order.date}</span>\n                  <span className={`order-status ${order.status === '已完成' ? 'completed' : ''}`}>\n                    {order.status}\n                  </span>\n                </div>\n                <div className=\"order-items\">\n                  {order.items.join(', ')}\n                </div>\n                <div className=\"order-total\">总计: ¥{order.total}</div>\n              </div>\n            ))}\n          </div>\n          <button className=\"view-all-btn\">查看全部订单</button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Profile;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvB,KAAM,CAAAC,OAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAACC,IAAI,CAAC,CAAGN,QAAQ,CAAC,CACtBO,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,aAAa,CACpBC,MAAM,CAAE,IAAI,CACZC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,IACV,CAAC,CAAC,CAEF,KAAM,CAACC,MAAM,CAAC,CAAGZ,QAAQ,CAAC,CACxB,CACEa,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,CAAC,OAAO,CAAE,MAAM,CAAC,CACxBC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,KACV,CAAC,CACD,CACEJ,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,CAAC,OAAO,CAAE,MAAM,CAAC,CACxBC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,KACV,CAAC,CACD,CACEJ,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,CAAC,QAAQ,CAAC,CACjBC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,KACV,CAAC,CACF,CAAC,CAEF,KAAM,CAAAC,SAAS,CAAG,CAChB,CAAEL,EAAE,CAAE,WAAW,CAAEN,IAAI,CAAE,MAAM,CAAEY,IAAI,CAAE,IAAK,CAAC,CAC7C,CAAEN,EAAE,CAAE,SAAS,CAAEN,IAAI,CAAE,MAAM,CAAEY,IAAI,CAAE,IAAK,CAAC,CAC3C,CAAEN,EAAE,CAAE,SAAS,CAAEN,IAAI,CAAE,KAAK,CAAEY,IAAI,CAAE,IAAK,CAAC,CAC1C,CAAEN,EAAE,CAAE,MAAM,CAAEN,IAAI,CAAE,MAAM,CAAEY,IAAI,CAAE,GAAI,CAAC,CACvC,CAAEN,EAAE,CAAE,UAAU,CAAEN,IAAI,CAAE,MAAM,CAAEY,IAAI,CAAE,IAAK,CAAC,CAC5C,CAAEN,EAAE,CAAE,UAAU,CAAEN,IAAI,CAAE,IAAI,CAAEY,IAAI,CAAE,IAAK,CAAC,CAC3C,CAED,mBACEjB,IAAA,QAAKkB,SAAS,CAAC,SAAS,CAAAC,QAAA,cACtBjB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjB,KAAA,QAAKgB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnB,IAAA,QAAKkB,SAAS,CAAC,QAAQ,CAAAC,QAAA,CAAEf,IAAI,CAACG,MAAM,CAAM,CAAC,cAC3CL,KAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BnB,IAAA,OAAIkB,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEf,IAAI,CAACC,IAAI,CAAK,CAAC,cAC1CL,IAAA,MAAGkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEf,IAAI,CAACE,KAAK,CAAI,CAAC,cAC1CJ,KAAA,QAAKgB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnB,IAAA,SAAMkB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEf,IAAI,CAACI,KAAK,CAAO,CAAC,cACjDN,KAAA,SAAMgB,SAAS,CAAC,QAAQ,CAAAC,QAAA,EAAC,gBAAI,CAACf,IAAI,CAACK,MAAM,EAAO,CAAC,EAC9C,CAAC,EACH,CAAC,EACH,CAAC,cACNT,IAAA,WAAQkB,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,cAAE,CAAQ,CAAC,EACrC,CAAC,cAENjB,KAAA,QAAKgB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnB,IAAA,QAAKkB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAET,MAAM,CAACU,MAAM,CAAM,CAAC,cAClDpB,IAAA,QAAKkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oBAAG,CAAK,CAAC,EAClC,CAAC,cACNjB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnB,IAAA,QAAKkB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAET,MAAM,CAACW,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACP,MAAM,GAAK,KAAK,CAAC,CAACK,MAAM,CAAM,CAAC,cAClFpB,IAAA,QAAKkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oBAAG,CAAK,CAAC,EAClC,CAAC,cACNjB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnB,IAAA,QAAKkB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEf,IAAI,CAACK,MAAM,CAAM,CAAC,cAChDT,IAAA,QAAKkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,EACjC,CAAC,EACH,CAAC,cAENjB,KAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BnB,IAAA,OAAIkB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,0BAAI,CAAI,CAAC,cACvCnB,IAAA,QAAKkB,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBH,SAAS,CAACO,GAAG,CAAEC,IAAI,eAClBtB,KAAA,WAAsBgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACzCnB,IAAA,SAAMkB,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEK,IAAI,CAACP,IAAI,CAAO,CAAC,cAC9CjB,IAAA,SAAMkB,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEK,IAAI,CAACnB,IAAI,CAAO,CAAC,cAC9CL,IAAA,SAAMkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,GAH1BK,IAAI,CAACb,EAIV,CACT,CAAC,CACC,CAAC,EACH,CAAC,cAENT,KAAA,QAAKgB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnB,IAAA,OAAIkB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,0BAAI,CAAI,CAAC,cACvCnB,IAAA,QAAKkB,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzBT,MAAM,CAACa,GAAG,CAAEE,KAAK,eAChBvB,KAAA,QAAoBgB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACxCjB,KAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BnB,IAAA,SAAMkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEM,KAAK,CAACb,IAAI,CAAO,CAAC,cAChDZ,IAAA,SAAMkB,SAAS,CAAE,gBAAgBO,KAAK,CAACV,MAAM,GAAK,KAAK,CAAG,WAAW,CAAG,EAAE,EAAG,CAAAI,QAAA,CAC1EM,KAAK,CAACV,MAAM,CACT,CAAC,EACJ,CAAC,cACNf,IAAA,QAAKkB,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzBM,KAAK,CAACZ,KAAK,CAACa,IAAI,CAAC,IAAI,CAAC,CACpB,CAAC,cACNxB,KAAA,QAAKgB,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,oBAAK,CAACM,KAAK,CAACX,KAAK,EAAM,CAAC,GAV7CW,KAAK,CAACd,EAWX,CACN,CAAC,CACC,CAAC,cACNX,IAAA,WAAQkB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,sCAAM,CAAQ,CAAC,EAC7C,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}