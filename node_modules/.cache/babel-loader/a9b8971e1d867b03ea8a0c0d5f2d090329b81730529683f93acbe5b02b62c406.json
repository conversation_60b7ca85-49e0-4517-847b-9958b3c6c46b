{"ast": null, "code": "import React from'react';import{Link,useLocation}from'react-router-dom';import'./BottomNav.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const BottomNav=()=>{const location=useLocation();const navItems=[{path:'/',label:'首页',icon:'🏠'},{path:'/menu',label:'菜单',icon:'📋'},{path:'/cart',label:'购物车',icon:'🛒'},{path:'/profile',label:'我的',icon:'👤'}];return/*#__PURE__*/_jsx(\"nav\",{className:\"bottom-nav\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bottom-nav-content\",children:navItems.map(item=>/*#__PURE__*/_jsxs(Link,{to:item.path,className:`nav-item ${location.pathname===item.path?'active':''}`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"nav-icon\",children:item.icon}),/*#__PURE__*/_jsx(\"span\",{className:\"nav-label\",children:item.label})]},item.path))})});};export default BottomNav;", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsx", "_jsx", "jsxs", "_jsxs", "BottomNav", "location", "navItems", "path", "label", "icon", "className", "children", "map", "item", "to", "pathname"], "sources": ["/Users/<USER>/Desktop/code/vibe/yidiandian/src/components/BottomNav.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport './BottomNav.css';\n\nconst BottomNav: React.FC = () => {\n  const location = useLocation();\n\n  const navItems = [\n    { path: '/', label: '首页', icon: '🏠' },\n    { path: '/menu', label: '菜单', icon: '📋' },\n    { path: '/cart', label: '购物车', icon: '🛒' },\n    { path: '/profile', label: '我的', icon: '👤' },\n  ];\n\n  return (\n    <nav className=\"bottom-nav\">\n      <div className=\"bottom-nav-content\">\n        {navItems.map((item) => (\n          <Link\n            key={item.path}\n            to={item.path}\n            className={`nav-item ${location.pathname === item.path ? 'active' : ''}`}\n          >\n            <span className=\"nav-icon\">{item.icon}</span>\n            <span className=\"nav-label\">{item.label}</span>\n          </Link>\n        ))}\n      </div>\n    </nav>\n  );\n};\n\nexport default BottomNav;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,MAAO,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzB,KAAM,CAAAC,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,QAAQ,CAAGN,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAO,QAAQ,CAAG,CACf,CAAEC,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,IAAI,CAAEC,IAAI,CAAE,IAAK,CAAC,CACtC,CAAEF,IAAI,CAAE,OAAO,CAAEC,KAAK,CAAE,IAAI,CAAEC,IAAI,CAAE,IAAK,CAAC,CAC1C,CAAEF,IAAI,CAAE,OAAO,CAAEC,KAAK,CAAE,KAAK,CAAEC,IAAI,CAAE,IAAK,CAAC,CAC3C,CAAEF,IAAI,CAAE,UAAU,CAAEC,KAAK,CAAE,IAAI,CAAEC,IAAI,CAAE,IAAK,CAAC,CAC9C,CAED,mBACER,IAAA,QAAKS,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBV,IAAA,QAAKS,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAChCL,QAAQ,CAACM,GAAG,CAAEC,IAAI,eACjBV,KAAA,CAACL,IAAI,EAEHgB,EAAE,CAAED,IAAI,CAACN,IAAK,CACdG,SAAS,CAAE,YAAYL,QAAQ,CAACU,QAAQ,GAAKF,IAAI,CAACN,IAAI,CAAG,QAAQ,CAAG,EAAE,EAAG,CAAAI,QAAA,eAEzEV,IAAA,SAAMS,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAEE,IAAI,CAACJ,IAAI,CAAO,CAAC,cAC7CR,IAAA,SAAMS,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEE,IAAI,CAACL,KAAK,CAAO,CAAC,GAL1CK,IAAI,CAACN,IAMN,CACP,CAAC,CACC,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}