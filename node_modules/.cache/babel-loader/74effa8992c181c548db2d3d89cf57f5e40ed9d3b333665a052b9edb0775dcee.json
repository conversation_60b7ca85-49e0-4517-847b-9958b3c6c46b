{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/vibe/yidiandian/src/pages/Home.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { drinks } from '../data/drinks';\nimport './Home.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  const featuredDrinks = drinks.slice(0, 3);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"hero-title\",\n          children: \"\\u4E00\\u70B9\\u70B9\\u5976\\u8336\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hero-subtitle\",\n          children: \"\\u65B0\\u9C9C\\u5236\\u4F5C\\uFF0C\\u6BCF\\u4E00\\u53E3\\u90FD\\u662F\\u5E78\\u798F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/menu\",\n          className: \"btn btn-primary\",\n          children: \"\\u7ACB\\u5373\\u70B9\\u9910\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"featured-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"\\u63A8\\u8350\\u996E\\u54C1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"featured-grid\",\n          children: featuredDrinks.map(drink => /*#__PURE__*/_jsxDEV(Link, {\n            to: `/drink/${drink.id}`,\n            className: \"featured-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"featured-image\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"placeholder-image\",\n                children: \"\\uD83E\\uDDCB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"featured-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"featured-name\",\n                children: drink.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"featured-description\",\n                children: drink.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"featured-price\",\n                children: [\"\\xA5\", drink.price]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 17\n            }, this)]\n          }, drink.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"promo-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"promo-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"promo-title\",\n            children: \"\\u65B0\\u7528\\u6237\\u4E13\\u4EAB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"promo-text\",\n            children: \"\\u9996\\u6B21\\u70B9\\u9910\\u7ACB\\u51CF5\\u5143\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/menu\",\n            className: \"btn btn-secondary\",\n            children: \"\\u53BB\\u70B9\\u9910\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "Link", "drinks", "jsxDEV", "_jsxDEV", "Home", "featured<PERSON><PERSON><PERSON>", "slice", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "map", "drink", "id", "name", "description", "price", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/vibe/yidiandian/src/pages/Home.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { drinks } from '../data/drinks';\nimport './Home.css';\n\nconst Home: React.FC = () => {\n  const featuredDrinks = drinks.slice(0, 3);\n\n  return (\n    <div className=\"home\">\n      <section className=\"hero\">\n        <div className=\"hero-content\">\n          <h1 className=\"hero-title\">一点点奶茶</h1>\n          <p className=\"hero-subtitle\">新鲜制作，每一口都是幸福</p>\n          <Link to=\"/menu\" className=\"btn btn-primary\">\n            立即点餐\n          </Link>\n        </div>\n      </section>\n\n      <section className=\"featured-section\">\n        <div className=\"container\">\n          <h2 className=\"section-title\">推荐饮品</h2>\n          <div className=\"featured-grid\">\n            {featuredDrinks.map((drink) => (\n              <Link key={drink.id} to={`/drink/${drink.id}`} className=\"featured-card\">\n                <div className=\"featured-image\">\n                  <div className=\"placeholder-image\">🧋</div>\n                </div>\n                <div className=\"featured-info\">\n                  <h3 className=\"featured-name\">{drink.name}</h3>\n                  <p className=\"featured-description\">{drink.description}</p>\n                  <div className=\"featured-price\">¥{drink.price}</div>\n                </div>\n              </Link>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      <section className=\"promo-section\">\n        <div className=\"container\">\n          <div className=\"promo-card\">\n            <h3 className=\"promo-title\">新用户专享</h3>\n            <p className=\"promo-text\">首次点餐立减5元</p>\n            <Link to=\"/menu\" className=\"btn btn-secondary\">\n              去点餐\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,gBAAgB;AACvC,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,IAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,cAAc,GAAGJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAEzC,oBACEH,OAAA;IAAKI,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBL,OAAA;MAASI,SAAS,EAAC,MAAM;MAAAC,QAAA,eACvBL,OAAA;QAAKI,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BL,OAAA;UAAII,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrCT,OAAA;UAAGI,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7CT,OAAA,CAACH,IAAI;UAACa,EAAE,EAAC,OAAO;UAACN,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVT,OAAA;MAASI,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCL,OAAA;QAAKI,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBL,OAAA;UAAII,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvCT,OAAA;UAAKI,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BH,cAAc,CAACS,GAAG,CAAEC,KAAK,iBACxBZ,OAAA,CAACH,IAAI;YAAgBa,EAAE,EAAE,UAAUE,KAAK,CAACC,EAAE,EAAG;YAACT,SAAS,EAAC,eAAe;YAAAC,QAAA,gBACtEL,OAAA;cAAKI,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7BL,OAAA;gBAAKI,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNT,OAAA;cAAKI,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BL,OAAA;gBAAII,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEO,KAAK,CAACE;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/CT,OAAA;gBAAGI,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAEO,KAAK,CAACG;cAAW;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DT,OAAA;gBAAKI,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAAC,MAAC,EAACO,KAAK,CAACI,KAAK;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA,GARGG,KAAK,CAACC,EAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASb,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVT,OAAA;MAASI,SAAS,EAAC,eAAe;MAAAC,QAAA,eAChCL,OAAA;QAAKI,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBL,OAAA;UAAKI,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBL,OAAA;YAAII,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtCT,OAAA;YAAGI,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtCT,OAAA,CAACH,IAAI;YAACa,EAAE,EAAC,OAAO;YAACN,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAE/C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACQ,EAAA,GAhDIhB,IAAc;AAkDpB,eAAeA,IAAI;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}