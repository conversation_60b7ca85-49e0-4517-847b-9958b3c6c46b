{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/vibe/yidiandian/src/components/Header.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useCart } from '../contexts/CartContext';\nimport './Header.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const location = useLocation();\n  const {\n    state\n  } = useCart();\n  const getPageTitle = () => {\n    switch (location.pathname) {\n      case '/':\n        return '一点点奶茶';\n      case '/menu':\n        return '菜单';\n      case '/cart':\n        return '购物车';\n      case '/profile':\n        return '我的';\n      default:\n        return '一点点奶茶';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-content\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"logo\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"logo-text\",\n          children: \"\\u4E00\\u70B9\\u70B9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"page-title\",\n        children: getPageTitle()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/cart\",\n        className: \"cart-link\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-icon\",\n          children: [\"\\uD83D\\uDED2\", state.totalItems > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"cart-badge\",\n            children: state.totalItems\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"klIWdTGbL6QX2cBYmWN6nauIAzM=\", false, function () {\n  return [useLocation, useCart];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "useCart", "jsxDEV", "_jsxDEV", "Header", "_s", "location", "state", "getPageTitle", "pathname", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "totalItems", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/vibe/yidiandian/src/components/Header.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useCart } from '../contexts/CartContext';\nimport './Header.css';\n\nconst Header: React.FC = () => {\n  const location = useLocation();\n  const { state } = useCart();\n\n  const getPageTitle = () => {\n    switch (location.pathname) {\n      case '/':\n        return '一点点奶茶';\n      case '/menu':\n        return '菜单';\n      case '/cart':\n        return '购物车';\n      case '/profile':\n        return '我的';\n      default:\n        return '一点点奶茶';\n    }\n  };\n\n  return (\n    <header className=\"header\">\n      <div className=\"header-content\">\n        <Link to=\"/\" className=\"logo\">\n          <span className=\"logo-text\">一点点</span>\n        </Link>\n        <h1 className=\"page-title\">{getPageTitle()}</h1>\n        <Link to=\"/cart\" className=\"cart-link\">\n          <div className=\"cart-icon\">\n            🛒\n            {state.totalItems > 0 && (\n              <span className=\"cart-badge\">{state.totalItems}</span>\n            )}\n          </div>\n        </Link>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEO;EAAM,CAAC,GAAGN,OAAO,CAAC,CAAC;EAE3B,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzB,QAAQF,QAAQ,CAACG,QAAQ;MACvB,KAAK,GAAG;QACN,OAAO,OAAO;MAChB,KAAK,OAAO;QACV,OAAO,IAAI;MACb,KAAK,OAAO;QACV,OAAO,KAAK;MACd,KAAK,UAAU;QACb,OAAO,IAAI;MACb;QACE,OAAO,OAAO;IAClB;EACF,CAAC;EAED,oBACEN,OAAA;IAAQO,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBR,OAAA;MAAKO,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BR,OAAA,CAACJ,IAAI;QAACa,EAAE,EAAC,GAAG;QAACF,SAAS,EAAC,MAAM;QAAAC,QAAA,eAC3BR,OAAA;UAAMO,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACPb,OAAA;QAAIO,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAEH,YAAY,CAAC;MAAC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChDb,OAAA,CAACJ,IAAI;QAACa,EAAE,EAAC,OAAO;QAACF,SAAS,EAAC,WAAW;QAAAC,QAAA,eACpCR,OAAA;UAAKO,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAC,cAEzB,EAACJ,KAAK,CAACU,UAAU,GAAG,CAAC,iBACnBd,OAAA;YAAMO,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEJ,KAAK,CAACU;UAAU;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACX,EAAA,CArCID,MAAgB;EAAA,QACHJ,WAAW,EACVC,OAAO;AAAA;AAAAiB,EAAA,GAFrBd,MAAgB;AAuCtB,eAAeA,MAAM;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}