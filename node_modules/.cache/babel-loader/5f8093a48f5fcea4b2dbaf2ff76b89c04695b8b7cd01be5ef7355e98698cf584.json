{"ast": null, "code": "\"use strict\";\n\nmodule.exports = function (url, options) {\n  if (!options) {\n    options = {};\n  }\n  if (!url) {\n    return url;\n  }\n  url = String(url.__esModule ? url.default : url);\n\n  // If url is already wrapped in quotes, remove them\n  if (/^['\"].*['\"]$/.test(url)) {\n    url = url.slice(1, -1);\n  }\n  if (options.hash) {\n    url += options.hash;\n  }\n\n  // Should url be wrapped?\n  // See https://drafts.csswg.org/css-values-3/#urls\n  if (/[\"'() \\t\\n]|(%20)/.test(url) || options.needQuotes) {\n    return \"\\\"\".concat(url.replace(/\"/g, '\\\\\"').replace(/\\n/g, \"\\\\n\"), \"\\\"\");\n  }\n  return url;\n};", "map": {"version": 3, "names": ["module", "exports", "url", "options", "String", "__esModule", "default", "test", "slice", "hash", "needQuotes", "concat", "replace"], "sources": ["/Users/<USER>/Desktop/code/vibe/yidiandian/node_modules/css-loader/dist/runtime/getUrl.js"], "sourcesContent": ["\"use strict\";\n\nmodule.exports = function (url, options) {\n  if (!options) {\n    options = {};\n  }\n  if (!url) {\n    return url;\n  }\n  url = String(url.__esModule ? url.default : url);\n\n  // If url is already wrapped in quotes, remove them\n  if (/^['\"].*['\"]$/.test(url)) {\n    url = url.slice(1, -1);\n  }\n  if (options.hash) {\n    url += options.hash;\n  }\n\n  // Should url be wrapped?\n  // See https://drafts.csswg.org/css-values-3/#urls\n  if (/[\"'() \\t\\n]|(%20)/.test(url) || options.needQuotes) {\n    return \"\\\"\".concat(url.replace(/\"/g, '\\\\\"').replace(/\\n/g, \"\\\\n\"), \"\\\"\");\n  }\n  return url;\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAEC,OAAO,EAAE;EACvC,IAAI,CAACA,OAAO,EAAE;IACZA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAI,CAACD,GAAG,EAAE;IACR,OAAOA,GAAG;EACZ;EACAA,GAAG,GAAGE,MAAM,CAACF,GAAG,CAACG,UAAU,GAAGH,GAAG,CAACI,OAAO,GAAGJ,GAAG,CAAC;;EAEhD;EACA,IAAI,cAAc,CAACK,IAAI,CAACL,GAAG,CAAC,EAAE;IAC5BA,GAAG,GAAGA,GAAG,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxB;EACA,IAAIL,OAAO,CAACM,IAAI,EAAE;IAChBP,GAAG,IAAIC,OAAO,CAACM,IAAI;EACrB;;EAEA;EACA;EACA,IAAI,mBAAmB,CAACF,IAAI,CAACL,GAAG,CAAC,IAAIC,OAAO,CAACO,UAAU,EAAE;IACvD,OAAO,IAAI,CAACC,MAAM,CAACT,GAAG,CAACU,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;EAC1E;EACA,OAAOV,GAAG;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}