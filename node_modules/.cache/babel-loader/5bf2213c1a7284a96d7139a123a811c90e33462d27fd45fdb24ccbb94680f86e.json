{"ast": null, "code": "import React from'react';import{<PERSON>rowserRouter as Router,Routes,Route}from'react-router-dom';import{CartProvider}from'./contexts/CartContext';import Layout from'./components/Layout';import Home from'./pages/Home';import Menu from'./pages/Menu';import DrinkDetail from'./pages/DrinkDetail';import Cart from'./pages/Cart';import Profile from'./pages/Profile';import'./App.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(CartProvider,{children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Home,{})}),/*#__PURE__*/_jsx(Route,{path:\"/menu\",element:/*#__PURE__*/_jsx(Menu,{})}),/*#__PURE__*/_jsx(Route,{path:\"/drink/:id\",element:/*#__PURE__*/_jsx(DrinkDetail,{})}),/*#__PURE__*/_jsx(Route,{path:\"/cart\",element:/*#__PURE__*/_jsx(Cart,{})}),/*#__PURE__*/_jsx(Route,{path:\"/profile\",element:/*#__PURE__*/_jsx(Profile,{})})]})})})})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "CartProvider", "Layout", "Home", "<PERSON><PERSON>", "DrinkDetail", "<PERSON><PERSON>", "Profile", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "path", "element"], "sources": ["/Users/<USER>/Desktop/code/vibe/yidiandian/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { CartProvider } from './contexts/CartContext';\nimport Layout from './components/Layout';\nimport Home from './pages/Home';\nimport Menu from './pages/Menu';\nimport DrinkDetail from './pages/DrinkDetail';\nimport Cart from './pages/Cart';\nimport Profile from './pages/Profile';\nimport './App.css';\n\nfunction App() {\n  return (\n    <CartProvider>\n      <Router>\n        <div className=\"App\">\n          <Layout>\n            <Routes>\n              <Route path=\"/\" element={<Home />} />\n              <Route path=\"/menu\" element={<Menu />} />\n              <Route path=\"/drink/:id\" element={<DrinkDetail />} />\n              <Route path=\"/cart\" element={<Cart />} />\n              <Route path=\"/profile\" element={<Profile />} />\n            </Routes>\n          </Layout>\n        </div>\n      </Router>\n    </CartProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CACzE,OAASC,YAAY,KAAQ,wBAAwB,CACrD,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAACR,YAAY,EAAAY,QAAA,cACXJ,IAAA,CAACX,MAAM,EAAAe,QAAA,cACLJ,IAAA,QAAKK,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBJ,IAAA,CAACP,MAAM,EAAAW,QAAA,cACLF,KAAA,CAACZ,MAAM,EAAAc,QAAA,eACLJ,IAAA,CAACT,KAAK,EAACe,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACN,IAAI,GAAE,CAAE,CAAE,CAAC,cACrCM,IAAA,CAACT,KAAK,EAACe,IAAI,CAAC,OAAO,CAACC,OAAO,cAAEP,IAAA,CAACL,IAAI,GAAE,CAAE,CAAE,CAAC,cACzCK,IAAA,CAACT,KAAK,EAACe,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEP,IAAA,CAACJ,WAAW,GAAE,CAAE,CAAE,CAAC,cACrDI,IAAA,CAACT,KAAK,EAACe,IAAI,CAAC,OAAO,CAACC,OAAO,cAAEP,IAAA,CAACH,IAAI,GAAE,CAAE,CAAE,CAAC,cACzCG,IAAA,CAACT,KAAK,EAACe,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEP,IAAA,CAACF,OAAO,GAAE,CAAE,CAAE,CAAC,EACzC,CAAC,CACH,CAAC,CACN,CAAC,CACA,CAAC,CACG,CAAC,CAEnB,CAEA,cAAe,CAAAK,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}