{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/vibe/yidiandian/src/pages/Menu.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { drinks, categories } from '../data/drinks';\nimport './Menu.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Menu = () => {\n  _s();\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const filteredDrinks = selectedCategory === 'all' ? drinks : drinks.filter(drink => drink.category === selectedCategory);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"menu\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"category-filter\",\n        children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `category-btn ${selectedCategory === category.id ? 'active' : ''}`,\n          onClick: () => setSelectedCategory(category.id),\n          children: category.name\n        }, category.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"drinks-grid\",\n        children: filteredDrinks.map(drink => /*#__PURE__*/_jsxDEV(Link, {\n          to: `/drink/${drink.id}`,\n          className: \"drink-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"drink-image\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"placeholder-image\",\n              children: \"\\uD83E\\uDDCB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"drink-category\",\n              children: drink.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"drink-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"drink-name\",\n              children: drink.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"drink-description\",\n              children: drink.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"drink-price\",\n              children: [\"\\xA5\", drink.price]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this)]\n        }, drink.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), filteredDrinks.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-icon\",\n          children: \"\\uD83E\\uDD64\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"empty-text\",\n          children: \"\\u8BE5\\u5206\\u7C7B\\u6682\\u65E0\\u996E\\u54C1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_s(Menu, \"ka1F1ceqEXioutdx48zEaS3nBME=\");\n_c = Menu;\nexport default Menu;\nvar _c;\n$RefreshReg$(_c, \"Menu\");", "map": {"version": 3, "names": ["React", "useState", "Link", "drinks", "categories", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "filteredDrinks", "filter", "drink", "category", "className", "children", "map", "id", "onClick", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "description", "price", "length", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/vibe/yidiandian/src/pages/Menu.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { drinks, categories } from '../data/drinks';\nimport './Menu.css';\n\nconst Menu: React.FC = () => {\n  const [selectedCategory, setSelectedCategory] = useState('all');\n\n  const filteredDrinks = selectedCategory === 'all' \n    ? drinks \n    : drinks.filter(drink => drink.category === selectedCategory);\n\n  return (\n    <div className=\"menu\">\n      <div className=\"container\">\n        <div className=\"category-filter\">\n          {categories.map((category) => (\n            <button\n              key={category.id}\n              className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}\n              onClick={() => setSelectedCategory(category.id)}\n            >\n              {category.name}\n            </button>\n          ))}\n        </div>\n\n        <div className=\"drinks-grid\">\n          {filteredDrinks.map((drink) => (\n            <Link key={drink.id} to={`/drink/${drink.id}`} className=\"drink-card\">\n              <div className=\"drink-image\">\n                <div className=\"placeholder-image\">🧋</div>\n                <div className=\"drink-category\">{drink.category}</div>\n              </div>\n              <div className=\"drink-info\">\n                <h3 className=\"drink-name\">{drink.name}</h3>\n                <p className=\"drink-description\">{drink.description}</p>\n                <div className=\"drink-price\">¥{drink.price}</div>\n              </div>\n            </Link>\n          ))}\n        </div>\n\n        {filteredDrinks.length === 0 && (\n          <div className=\"empty-state\">\n            <div className=\"empty-icon\">🥤</div>\n            <p className=\"empty-text\">该分类暂无饮品</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Menu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,EAAEC,UAAU,QAAQ,gBAAgB;AACnD,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,IAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMU,cAAc,GAAGF,gBAAgB,KAAK,KAAK,GAC7CN,MAAM,GACNA,MAAM,CAACS,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,KAAKL,gBAAgB,CAAC;EAE/D,oBACEH,OAAA;IAAKS,SAAS,EAAC,MAAM;IAAAC,QAAA,eACnBV,OAAA;MAAKS,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBV,OAAA;QAAKS,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7BZ,UAAU,CAACa,GAAG,CAAEH,QAAQ,iBACvBR,OAAA;UAEES,SAAS,EAAE,gBAAgBN,gBAAgB,KAAKK,QAAQ,CAACI,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC9EC,OAAO,EAAEA,CAAA,KAAMT,mBAAmB,CAACI,QAAQ,CAACI,EAAE,CAAE;UAAAF,QAAA,EAE/CF,QAAQ,CAACM;QAAI,GAJTN,QAAQ,CAACI,EAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKV,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENlB,OAAA;QAAKS,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBL,cAAc,CAACM,GAAG,CAAEJ,KAAK,iBACxBP,OAAA,CAACJ,IAAI;UAAgBuB,EAAE,EAAE,UAAUZ,KAAK,CAACK,EAAE,EAAG;UAACH,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACnEV,OAAA;YAAKS,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BV,OAAA;cAAKS,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3ClB,OAAA;cAAKS,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAEH,KAAK,CAACC;YAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNlB,OAAA;YAAKS,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBV,OAAA;cAAIS,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEH,KAAK,CAACO;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5ClB,OAAA;cAAGS,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAEH,KAAK,CAACa;YAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDlB,OAAA;cAAKS,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAC,MAAC,EAACH,KAAK,CAACc,KAAK;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA,GATGX,KAAK,CAACK,EAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUb,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELb,cAAc,CAACiB,MAAM,KAAK,CAAC,iBAC1BtB,OAAA;QAAKS,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BV,OAAA;UAAKS,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpClB,OAAA;UAAGS,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CA/CID,IAAc;AAAAsB,EAAA,GAAdtB,IAAc;AAiDpB,eAAeA,IAAI;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}