{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/vibe/yidiandian/src/components/Layout.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useLocation } from 'react-router-dom';\nimport Header from './Header';\nimport BottomNav from './BottomNav';\nimport './Layout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const location = useLocation();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(BottomNav, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useLocation", "Header", "BottomNav", "jsxDEV", "_jsxDEV", "Layout", "children", "_s", "location", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/vibe/yidiandian/src/components/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport { useLocation } from 'react-router-dom';\nimport Header from './Header';\nimport BottomNav from './BottomNav';\nimport './Layout.css';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const location = useLocation();\n\n  return (\n    <div className=\"layout\">\n      <Header />\n      <main className=\"main-content\">\n        {children}\n      </main>\n      <BottomNav />\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMtB,MAAMC,MAA6B,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtD,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,oBACEI,OAAA;IAAKK,SAAS,EAAC,QAAQ;IAAAH,QAAA,gBACrBF,OAAA,CAACH,MAAM;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVT,OAAA;MAAMK,SAAS,EAAC,cAAc;MAAAH,QAAA,EAC3BA;IAAQ;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACPT,OAAA,CAACF,SAAS;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACN,EAAA,CAZIF,MAA6B;EAAA,QAChBL,WAAW;AAAA;AAAAc,EAAA,GADxBT,MAA6B;AAcnC,eAAeA,MAAM;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}