{"ast": null, "code": "import React from'react';import{Link}from'react-router-dom';import{useCart}from'../contexts/CartContext';import'./Cart.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Cart=()=>{const{state,dispatch}=useCart();const handleUpdateQuantity=(id,newQuantity)=>{if(newQuantity<=0){dispatch({type:'REMOVE_ITEM',payload:id});}else{dispatch({type:'UPDATE_QUANTITY',payload:{id,quantity:newQuantity}});}};const handleRemoveItem=id=>{dispatch({type:'REMOVE_ITEM',payload:id});};const handleClearCart=()=>{dispatch({type:'CLEAR_CART'});};const handleCheckout=()=>{// In a real app, this would navigate to a checkout page\nalert('感谢您的订单！这是演示版本，实际应用中会跳转到结账页面。');dispatch({type:'CLEAR_CART'});};if(state.items.length===0){return/*#__PURE__*/_jsx(\"div\",{className:\"cart\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"empty-cart\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"empty-icon\",children:\"\\uD83D\\uDED2\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"empty-title\",children:\"\\u8D2D\\u7269\\u8F66\\u662F\\u7A7A\\u7684\"}),/*#__PURE__*/_jsx(\"p\",{className:\"empty-text\",children:\"\\u53BB\\u83DC\\u5355\\u770B\\u770B\\u6709\\u4EC0\\u4E48\\u597D\\u559D\\u7684\\u996E\\u54C1\\u5427\"}),/*#__PURE__*/_jsx(Link,{to:\"/menu\",className:\"btn btn-primary\",children:\"\\u6D4F\\u89C8\\u83DC\\u5355\"})]})})});}return/*#__PURE__*/_jsx(\"div\",{className:\"cart\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"cart-header\",children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"cart-title\",children:[\"\\u8D2D\\u7269\\u8F66 (\",state.totalItems,\")\"]}),/*#__PURE__*/_jsx(\"button\",{className:\"clear-btn\",onClick:handleClearCart,children:\"\\u6E05\\u7A7A\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"cart-items\",children:state.items.map(item=>/*#__PURE__*/_jsxs(\"div\",{className:\"cart-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"item-image\",children:/*#__PURE__*/_jsx(\"div\",{className:\"placeholder-image\",children:\"\\uD83E\\uDDCB\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"item-details\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"item-name\",children:item.drink.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"item-options\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"option-tag\",children:item.size.name}),/*#__PURE__*/_jsx(\"span\",{className:\"option-tag\",children:item.sweetness.name}),/*#__PURE__*/_jsx(\"span\",{className:\"option-tag\",children:item.ice.name}),item.toppings.length>0&&/*#__PURE__*/_jsx(\"span\",{className:\"option-tag\",children:item.toppings.map(t=>t.name).join(', ')})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"item-price\",children:[\"\\xA5\",item.totalPrice]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"item-controls\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"quantity-controls\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"quantity-btn\",onClick:()=>handleUpdateQuantity(item.id,item.quantity-1),children:\"-\"}),/*#__PURE__*/_jsx(\"span\",{className:\"quantity-value\",children:item.quantity}),/*#__PURE__*/_jsx(\"button\",{className:\"quantity-btn\",onClick:()=>handleUpdateQuantity(item.id,item.quantity+1),children:\"+\"})]}),/*#__PURE__*/_jsx(\"button\",{className:\"remove-btn\",onClick:()=>handleRemoveItem(item.id),children:\"\\uD83D\\uDDD1\\uFE0F\"})]})]},item.id))}),/*#__PURE__*/_jsxs(\"div\",{className:\"cart-summary\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"summary-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"summary-label\",children:\"\\u5546\\u54C1\\u603B\\u8BA1\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"summary-value\",children:[\"\\xA5\",state.totalPrice]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"summary-label\",children:\"\\u914D\\u9001\\u8D39\"}),/*#__PURE__*/_jsx(\"span\",{className:\"summary-value\",children:\"\\xA53\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-row total-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"summary-label\",children:\"\\u603B\\u8BA1\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"summary-value\",children:[\"\\xA5\",state.totalPrice+3]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"checkout-section\",children:/*#__PURE__*/_jsxs(\"button\",{className:\"btn btn-primary checkout-btn\",onClick:handleCheckout,children:[\"\\u53BB\\u7ED3\\u8D26 (\\xA5\",state.totalPrice+3,\")\"]})})]})});};export default Cart;", "map": {"version": 3, "names": ["React", "Link", "useCart", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "state", "dispatch", "handleUpdateQuantity", "id", "newQuantity", "type", "payload", "quantity", "handleRemoveItem", "handleClearCart", "handleCheckout", "alert", "items", "length", "className", "children", "to", "totalItems", "onClick", "map", "item", "drink", "name", "size", "sweetness", "ice", "toppings", "t", "join", "totalPrice"], "sources": ["/Users/<USER>/Desktop/code/vibe/yidiandian/src/pages/Cart.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { useCart } from '../contexts/CartContext';\nimport './Cart.css';\n\nconst Cart: React.FC = () => {\n  const { state, dispatch } = useCart();\n\n  const handleUpdateQuantity = (id: string, newQuantity: number) => {\n    if (newQuantity <= 0) {\n      dispatch({ type: 'REMOVE_ITEM', payload: id });\n    } else {\n      dispatch({ type: 'UPDATE_QUANTITY', payload: { id, quantity: newQuantity } });\n    }\n  };\n\n  const handleRemoveItem = (id: string) => {\n    dispatch({ type: 'REMOVE_ITEM', payload: id });\n  };\n\n  const handleClearCart = () => {\n    dispatch({ type: 'CLEAR_CART' });\n  };\n\n  const handleCheckout = () => {\n    // In a real app, this would navigate to a checkout page\n    alert('感谢您的订单！这是演示版本，实际应用中会跳转到结账页面。');\n    dispatch({ type: 'CLEAR_CART' });\n  };\n\n  if (state.items.length === 0) {\n    return (\n      <div className=\"cart\">\n        <div className=\"container\">\n          <div className=\"empty-cart\">\n            <div className=\"empty-icon\">🛒</div>\n            <h2 className=\"empty-title\">购物车是空的</h2>\n            <p className=\"empty-text\">去菜单看看有什么好喝的饮品吧</p>\n            <Link to=\"/menu\" className=\"btn btn-primary\">\n              浏览菜单\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"cart\">\n      <div className=\"container\">\n        <div className=\"cart-header\">\n          <h2 className=\"cart-title\">购物车 ({state.totalItems})</h2>\n          <button className=\"clear-btn\" onClick={handleClearCart}>\n            清空\n          </button>\n        </div>\n\n        <div className=\"cart-items\">\n          {state.items.map((item) => (\n            <div key={item.id} className=\"cart-item\">\n              <div className=\"item-image\">\n                <div className=\"placeholder-image\">🧋</div>\n              </div>\n              \n              <div className=\"item-details\">\n                <h3 className=\"item-name\">{item.drink.name}</h3>\n                <div className=\"item-options\">\n                  <span className=\"option-tag\">{item.size.name}</span>\n                  <span className=\"option-tag\">{item.sweetness.name}</span>\n                  <span className=\"option-tag\">{item.ice.name}</span>\n                  {item.toppings.length > 0 && (\n                    <span className=\"option-tag\">\n                      {item.toppings.map(t => t.name).join(', ')}\n                    </span>\n                  )}\n                </div>\n                <div className=\"item-price\">¥{item.totalPrice}</div>\n              </div>\n\n              <div className=\"item-controls\">\n                <div className=\"quantity-controls\">\n                  <button\n                    className=\"quantity-btn\"\n                    onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}\n                  >\n                    -\n                  </button>\n                  <span className=\"quantity-value\">{item.quantity}</span>\n                  <button\n                    className=\"quantity-btn\"\n                    onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}\n                  >\n                    +\n                  </button>\n                </div>\n                <button\n                  className=\"remove-btn\"\n                  onClick={() => handleRemoveItem(item.id)}\n                >\n                  🗑️\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"cart-summary\">\n          <div className=\"summary-row\">\n            <span className=\"summary-label\">商品总计</span>\n            <span className=\"summary-value\">¥{state.totalPrice}</span>\n          </div>\n          <div className=\"summary-row\">\n            <span className=\"summary-label\">配送费</span>\n            <span className=\"summary-value\">¥3</span>\n          </div>\n          <div className=\"summary-row total-row\">\n            <span className=\"summary-label\">总计</span>\n            <span className=\"summary-value\">¥{state.totalPrice + 3}</span>\n          </div>\n        </div>\n\n        <div className=\"checkout-section\">\n          <button className=\"btn btn-primary checkout-btn\" onClick={handleCheckout}>\n            去结账 (¥{state.totalPrice + 3})\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Cart;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,OAAO,KAAQ,yBAAyB,CACjD,MAAO,YAAY,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpB,KAAM,CAAAC,IAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAEC,KAAK,CAAEC,QAAS,CAAC,CAAGP,OAAO,CAAC,CAAC,CAErC,KAAM,CAAAQ,oBAAoB,CAAGA,CAACC,EAAU,CAAEC,WAAmB,GAAK,CAChE,GAAIA,WAAW,EAAI,CAAC,CAAE,CACpBH,QAAQ,CAAC,CAAEI,IAAI,CAAE,aAAa,CAAEC,OAAO,CAAEH,EAAG,CAAC,CAAC,CAChD,CAAC,IAAM,CACLF,QAAQ,CAAC,CAAEI,IAAI,CAAE,iBAAiB,CAAEC,OAAO,CAAE,CAAEH,EAAE,CAAEI,QAAQ,CAAEH,WAAY,CAAE,CAAC,CAAC,CAC/E,CACF,CAAC,CAED,KAAM,CAAAI,gBAAgB,CAAIL,EAAU,EAAK,CACvCF,QAAQ,CAAC,CAAEI,IAAI,CAAE,aAAa,CAAEC,OAAO,CAAEH,EAAG,CAAC,CAAC,CAChD,CAAC,CAED,KAAM,CAAAM,eAAe,CAAGA,CAAA,GAAM,CAC5BR,QAAQ,CAAC,CAAEI,IAAI,CAAE,YAAa,CAAC,CAAC,CAClC,CAAC,CAED,KAAM,CAAAK,cAAc,CAAGA,CAAA,GAAM,CAC3B;AACAC,KAAK,CAAC,8BAA8B,CAAC,CACrCV,QAAQ,CAAC,CAAEI,IAAI,CAAE,YAAa,CAAC,CAAC,CAClC,CAAC,CAED,GAAIL,KAAK,CAACY,KAAK,CAACC,MAAM,GAAK,CAAC,CAAE,CAC5B,mBACEjB,IAAA,QAAKkB,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBnB,IAAA,QAAKkB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBjB,KAAA,QAAKgB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnB,IAAA,QAAKkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACpCnB,IAAA,OAAIkB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,sCAAM,CAAI,CAAC,cACvCnB,IAAA,MAAGkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,sFAAc,CAAG,CAAC,cAC5CnB,IAAA,CAACH,IAAI,EAACuB,EAAE,CAAC,OAAO,CAACF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,0BAE7C,CAAM,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAEA,mBACEnB,IAAA,QAAKkB,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBjB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjB,KAAA,QAAKgB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjB,KAAA,OAAIgB,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,sBAAK,CAACf,KAAK,CAACiB,UAAU,CAAC,GAAC,EAAI,CAAC,cACxDrB,IAAA,WAAQkB,SAAS,CAAC,WAAW,CAACI,OAAO,CAAET,eAAgB,CAAAM,QAAA,CAAC,cAExD,CAAQ,CAAC,EACN,CAAC,cAENnB,IAAA,QAAKkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxBf,KAAK,CAACY,KAAK,CAACO,GAAG,CAAEC,IAAI,eACpBtB,KAAA,QAAmBgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACtCnB,IAAA,QAAKkB,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBnB,IAAA,QAAKkB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,CACxC,CAAC,cAENjB,KAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BnB,IAAA,OAAIkB,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEK,IAAI,CAACC,KAAK,CAACC,IAAI,CAAK,CAAC,cAChDxB,KAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BnB,IAAA,SAAMkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEK,IAAI,CAACG,IAAI,CAACD,IAAI,CAAO,CAAC,cACpD1B,IAAA,SAAMkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEK,IAAI,CAACI,SAAS,CAACF,IAAI,CAAO,CAAC,cACzD1B,IAAA,SAAMkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEK,IAAI,CAACK,GAAG,CAACH,IAAI,CAAO,CAAC,CAClDF,IAAI,CAACM,QAAQ,CAACb,MAAM,CAAG,CAAC,eACvBjB,IAAA,SAAMkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CACzBK,IAAI,CAACM,QAAQ,CAACP,GAAG,CAACQ,CAAC,EAAIA,CAAC,CAACL,IAAI,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC,CACtC,CACP,EACE,CAAC,cACN9B,KAAA,QAAKgB,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,MAAC,CAACK,IAAI,CAACS,UAAU,EAAM,CAAC,EACjD,CAAC,cAEN/B,KAAA,QAAKgB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjB,KAAA,QAAKgB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCnB,IAAA,WACEkB,SAAS,CAAC,cAAc,CACxBI,OAAO,CAAEA,CAAA,GAAMhB,oBAAoB,CAACkB,IAAI,CAACjB,EAAE,CAAEiB,IAAI,CAACb,QAAQ,CAAG,CAAC,CAAE,CAAAQ,QAAA,CACjE,GAED,CAAQ,CAAC,cACTnB,IAAA,SAAMkB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAEK,IAAI,CAACb,QAAQ,CAAO,CAAC,cACvDX,IAAA,WACEkB,SAAS,CAAC,cAAc,CACxBI,OAAO,CAAEA,CAAA,GAAMhB,oBAAoB,CAACkB,IAAI,CAACjB,EAAE,CAAEiB,IAAI,CAACb,QAAQ,CAAG,CAAC,CAAE,CAAAQ,QAAA,CACjE,GAED,CAAQ,CAAC,EACN,CAAC,cACNnB,IAAA,WACEkB,SAAS,CAAC,YAAY,CACtBI,OAAO,CAAEA,CAAA,GAAMV,gBAAgB,CAACY,IAAI,CAACjB,EAAE,CAAE,CAAAY,QAAA,CAC1C,oBAED,CAAQ,CAAC,EACN,CAAC,GA1CEK,IAAI,CAACjB,EA2CV,CACN,CAAC,CACC,CAAC,cAENL,KAAA,QAAKgB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BjB,KAAA,QAAKgB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BnB,IAAA,SAAMkB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,cAC3CjB,KAAA,SAAMgB,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,MAAC,CAACf,KAAK,CAAC6B,UAAU,EAAO,CAAC,EACvD,CAAC,cACN/B,KAAA,QAAKgB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BnB,IAAA,SAAMkB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,oBAAG,CAAM,CAAC,cAC1CnB,IAAA,SAAMkB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,OAAE,CAAM,CAAC,EACtC,CAAC,cACNjB,KAAA,QAAKgB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCnB,IAAA,SAAMkB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cACzCjB,KAAA,SAAMgB,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,MAAC,CAACf,KAAK,CAAC6B,UAAU,CAAG,CAAC,EAAO,CAAC,EAC3D,CAAC,EACH,CAAC,cAENjC,IAAA,QAAKkB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BjB,KAAA,WAAQgB,SAAS,CAAC,8BAA8B,CAACI,OAAO,CAAER,cAAe,CAAAK,QAAA,EAAC,0BAClE,CAACf,KAAK,CAAC6B,UAAU,CAAG,CAAC,CAAC,GAC9B,EAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA9B,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}