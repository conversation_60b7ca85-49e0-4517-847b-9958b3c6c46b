{"ast": null, "code": "import React from'react';import{useLocation}from'react-router-dom';import Header from'./Header';import BottomNav from'./BottomNav';import'./Layout.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Layout=_ref=>{let{children}=_ref;const location=useLocation();return/*#__PURE__*/_jsxs(\"div\",{className:\"layout\",children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsx(\"main\",{className:\"main-content\",children:children}),/*#__PURE__*/_jsx(BottomNav,{})]});};export default Layout;", "map": {"version": 3, "names": ["React", "useLocation", "Header", "BottomNav", "jsx", "_jsx", "jsxs", "_jsxs", "Layout", "_ref", "children", "location", "className"], "sources": ["/Users/<USER>/Desktop/code/vibe/yidiandian/src/components/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport { useLocation } from 'react-router-dom';\nimport Header from './Header';\nimport BottomNav from './BottomNav';\nimport './Layout.css';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const location = useLocation();\n\n  return (\n    <div className=\"layout\">\n      <Header />\n      <main className=\"main-content\">\n        {children}\n      </main>\n      <BottomNav />\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,UAAU,CAC7B,MAAO,CAAAC,SAAS,KAAM,aAAa,CACnC,MAAO,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMtB,KAAM,CAAAC,MAA6B,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACjD,KAAM,CAAAE,QAAQ,CAAGV,WAAW,CAAC,CAAC,CAE9B,mBACEM,KAAA,QAAKK,SAAS,CAAC,QAAQ,CAAAF,QAAA,eACrBL,IAAA,CAACH,MAAM,GAAE,CAAC,cACVG,IAAA,SAAMO,SAAS,CAAC,cAAc,CAAAF,QAAA,CAC3BA,QAAQ,CACL,CAAC,cACPL,IAAA,CAACF,SAAS,GAAE,CAAC,EACV,CAAC,CAEV,CAAC,CAED,cAAe,CAAAK,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}