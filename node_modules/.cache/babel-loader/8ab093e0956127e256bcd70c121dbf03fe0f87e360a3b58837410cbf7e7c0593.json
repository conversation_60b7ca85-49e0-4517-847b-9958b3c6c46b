{"ast": null, "code": "import React from'react';import{Link}from'react-router-dom';import{drinks}from'../data/drinks';import'./Home.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Home=()=>{const featuredDrinks=drinks.slice(0,3);return/*#__PURE__*/_jsxs(\"div\",{className:\"home\",children:[/*#__PURE__*/_jsx(\"section\",{className:\"hero\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"hero-content\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"hero-title\",children:\"\\u4E00\\u70B9\\u70B9\\u5976\\u8336\"}),/*#__PURE__*/_jsx(\"p\",{className:\"hero-subtitle\",children:\"\\u65B0\\u9C9C\\u5236\\u4F5C\\uFF0C\\u6BCF\\u4E00\\u53E3\\u90FD\\u662F\\u5E78\\u798F\"}),/*#__PURE__*/_jsx(Link,{to:\"/menu\",className:\"btn btn-primary\",children:\"\\u7ACB\\u5373\\u70B9\\u9910\"})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"featured-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"section-title\",children:\"\\u63A8\\u8350\\u996E\\u54C1\"}),/*#__PURE__*/_jsx(\"div\",{className:\"featured-grid\",children:featuredDrinks.map(drink=>/*#__PURE__*/_jsxs(Link,{to:`/drink/${drink.id}`,className:\"featured-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"featured-image\",children:/*#__PURE__*/_jsx(\"div\",{className:\"placeholder-image\",children:\"\\uD83E\\uDDCB\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"featured-info\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"featured-name\",children:drink.name}),/*#__PURE__*/_jsx(\"p\",{className:\"featured-description\",children:drink.description}),/*#__PURE__*/_jsxs(\"div\",{className:\"featured-price\",children:[\"\\xA5\",drink.price]})]})]},drink.id))})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"promo-section\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"promo-card\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"promo-title\",children:\"\\u65B0\\u7528\\u6237\\u4E13\\u4EAB\"}),/*#__PURE__*/_jsx(\"p\",{className:\"promo-text\",children:\"\\u9996\\u6B21\\u70B9\\u9910\\u7ACB\\u51CF5\\u5143\"}),/*#__PURE__*/_jsx(Link,{to:\"/menu\",className:\"btn btn-secondary\",children:\"\\u53BB\\u70B9\\u9910\"})]})})})]});};export default Home;", "map": {"version": 3, "names": ["React", "Link", "drinks", "jsx", "_jsx", "jsxs", "_jsxs", "Home", "featured<PERSON><PERSON><PERSON>", "slice", "className", "children", "to", "map", "drink", "id", "name", "description", "price"], "sources": ["/Users/<USER>/Desktop/code/vibe/yidiandian/src/pages/Home.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { drinks } from '../data/drinks';\nimport './Home.css';\n\nconst Home: React.FC = () => {\n  const featuredDrinks = drinks.slice(0, 3);\n\n  return (\n    <div className=\"home\">\n      <section className=\"hero\">\n        <div className=\"hero-content\">\n          <h1 className=\"hero-title\">一点点奶茶</h1>\n          <p className=\"hero-subtitle\">新鲜制作，每一口都是幸福</p>\n          <Link to=\"/menu\" className=\"btn btn-primary\">\n            立即点餐\n          </Link>\n        </div>\n      </section>\n\n      <section className=\"featured-section\">\n        <div className=\"container\">\n          <h2 className=\"section-title\">推荐饮品</h2>\n          <div className=\"featured-grid\">\n            {featuredDrinks.map((drink) => (\n              <Link key={drink.id} to={`/drink/${drink.id}`} className=\"featured-card\">\n                <div className=\"featured-image\">\n                  <div className=\"placeholder-image\">🧋</div>\n                </div>\n                <div className=\"featured-info\">\n                  <h3 className=\"featured-name\">{drink.name}</h3>\n                  <p className=\"featured-description\">{drink.description}</p>\n                  <div className=\"featured-price\">¥{drink.price}</div>\n                </div>\n              </Link>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      <section className=\"promo-section\">\n        <div className=\"container\">\n          <div className=\"promo-card\">\n            <h3 className=\"promo-title\">新用户专享</h3>\n            <p className=\"promo-text\">首次点餐立减5元</p>\n            <Link to=\"/menu\" className=\"btn btn-secondary\">\n              去点餐\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,MAAM,KAAQ,gBAAgB,CACvC,MAAO,YAAY,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpB,KAAM,CAAAC,IAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,cAAc,CAAGN,MAAM,CAACO,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAEzC,mBACEH,KAAA,QAAKI,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBP,IAAA,YAASM,SAAS,CAAC,MAAM,CAAAC,QAAA,cACvBL,KAAA,QAAKI,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BP,IAAA,OAAIM,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,gCAAK,CAAI,CAAC,cACrCP,IAAA,MAAGM,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,0EAAY,CAAG,CAAC,cAC7CP,IAAA,CAACH,IAAI,EAACW,EAAE,CAAC,OAAO,CAACF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,0BAE7C,CAAM,CAAC,EACJ,CAAC,CACC,CAAC,cAEVP,IAAA,YAASM,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cACnCL,KAAA,QAAKI,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBP,IAAA,OAAIM,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,0BAAI,CAAI,CAAC,cACvCP,IAAA,QAAKM,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3BH,cAAc,CAACK,GAAG,CAAEC,KAAK,eACxBR,KAAA,CAACL,IAAI,EAAgBW,EAAE,CAAE,UAAUE,KAAK,CAACC,EAAE,EAAG,CAACL,SAAS,CAAC,eAAe,CAAAC,QAAA,eACtEP,IAAA,QAAKM,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BP,IAAA,QAAKM,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,CACxC,CAAC,cACNL,KAAA,QAAKI,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BP,IAAA,OAAIM,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEG,KAAK,CAACE,IAAI,CAAK,CAAC,cAC/CZ,IAAA,MAAGM,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAEG,KAAK,CAACG,WAAW,CAAI,CAAC,cAC3DX,KAAA,QAAKI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAAC,MAAC,CAACG,KAAK,CAACI,KAAK,EAAM,CAAC,EACjD,CAAC,GARGJ,KAAK,CAACC,EASX,CACP,CAAC,CACC,CAAC,EACH,CAAC,CACC,CAAC,cAEVX,IAAA,YAASM,SAAS,CAAC,eAAe,CAAAC,QAAA,cAChCP,IAAA,QAAKM,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBL,KAAA,QAAKI,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBP,IAAA,OAAIM,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,gCAAK,CAAI,CAAC,cACtCP,IAAA,MAAGM,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,6CAAQ,CAAG,CAAC,cACtCP,IAAA,CAACH,IAAI,EAACW,EAAE,CAAC,OAAO,CAACF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,oBAE/C,CAAM,CAAC,EACJ,CAAC,CACH,CAAC,CACC,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}